#!/usr/bin/env python3
"""
BoltBug Demo Script
Demonstrates BoltBug's error detection capabilities.
"""
import time
import sys
import os

def demo_introduction():
    """Show demo introduction."""
    print("🐛 BoltBug Demo")
    print("=" * 50)
    print("This demo will show you how BoltBug detects and helps with Python errors.")
    print("Make sure BoltBug is running before proceeding!")
    print()
    
    response = input("Is BoltBug running? (y/n): ").lower().strip()
    if response != 'y':
        print("Please start BoltBug first by running:")
        print("  python run_boltbug.py")
        print("Then run this demo again.")
        sys.exit(0)

def create_error_file(filename, content):
    """Create a Python file with an error."""
    print(f"📝 Creating {filename} with an error...")
    with open(filename, 'w') as f:
        f.write(content)
    print(f"✅ {filename} created")
    time.sleep(2)  # Give BoltBug time to detect

def demo_syntax_error():
    """Demonstrate syntax error detection."""
    print("\n🔍 Demo 1: Syntax Error")
    print("-" * 30)
    
    error_code = '''# This file has a syntax error
def hello_world()  # Missing colon!
    print("Hello, World!")

hello_world()
'''
    
    create_error_file("demo_syntax_error.py", error_code)
    print("👀 BoltBug should have detected the missing colon!")
    input("Press Enter to continue...")

def demo_name_error():
    """Demonstrate name error detection."""
    print("\n🔍 Demo 2: Name Error")
    print("-" * 30)
    
    error_code = '''# This file has a name error
def greet_user():
    print(f"Hello, {username}!")  # username is not defined!

greet_user()
'''
    
    create_error_file("demo_name_error.py", error_code)
    print("👀 BoltBug should have detected the undefined variable!")
    input("Press Enter to continue...")

def demo_type_error():
    """Demonstrate type error detection."""
    print("\n🔍 Demo 3: Type Error")
    print("-" * 30)
    
    error_code = '''# This file has a type error
def add_numbers():
    result = "5" + 3  # Can't add string and number!
    print(result)

add_numbers()
'''
    
    create_error_file("demo_type_error.py", error_code)
    print("👀 BoltBug should have detected the type mismatch!")
    input("Press Enter to continue...")

def demo_index_error():
    """Demonstrate index error detection."""
    print("\n🔍 Demo 4: Index Error")
    print("-" * 30)
    
    error_code = '''# This file has an index error
def access_list():
    my_list = [1, 2, 3]
    print(my_list[5])  # Index 5 doesn't exist!

access_list()
'''
    
    create_error_file("demo_index_error.py", error_code)
    print("👀 BoltBug should have detected the index out of range!")
    input("Press Enter to continue...")

def cleanup_demo_files():
    """Clean up demo files."""
    print("\n🧹 Cleaning up demo files...")
    demo_files = [
        "demo_syntax_error.py",
        "demo_name_error.py", 
        "demo_type_error.py",
        "demo_index_error.py"
    ]
    
    for filename in demo_files:
        try:
            if os.path.exists(filename):
                os.remove(filename)
                print(f"🗑️  Removed {filename}")
        except Exception as e:
            print(f"⚠️  Could not remove {filename}: {e}")

def demo_conclusion():
    """Show demo conclusion."""
    print("\n🎉 Demo Complete!")
    print("=" * 50)
    print("You've seen how BoltBug can help with common Python errors:")
    print("  ✅ Syntax errors (missing colons, parentheses)")
    print("  ✅ Name errors (undefined variables)")
    print("  ✅ Type errors (incompatible operations)")
    print("  ✅ Index errors (out of range access)")
    print()
    print("💡 BoltBug Features:")
    print("  🎯 Real-time error detection")
    print("  🎨 Visual feedback with animations")
    print("  📚 Beginner-friendly explanations")
    print("  💾 Error logging for learning")
    print()
    print("🚀 Try creating your own Python files with errors!")
    print("📖 Check README.md for more information")

def main():
    """Main demo function."""
    try:
        demo_introduction()
        demo_syntax_error()
        demo_name_error()
        demo_type_error()
        demo_index_error()
        cleanup_demo_files()
        demo_conclusion()
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted by user")
        cleanup_demo_files()
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        cleanup_demo_files()

if __name__ == "__main__":
    main()
