"""
Configuration settings for BoltBug desktop companion.
"""
import os
import json
from pathlib import Path

class Config:
    def __init__(self):
        self.config_dir = Path.home() / ".boltbug"
        self.config_file = self.config_dir / "config.json"
        self.log_file = self.config_dir / "boltbug.log"
        
        # Default settings
        self.default_config = {
            "sprite_size": 64,
            "movement_speed": 2,
            "random_movement_interval": 100,  # milliseconds
            "error_detection_interval": 1000,  # milliseconds
            "monitor_directories": [str(Path.home())],
            "python_extensions": [".py"],
            "sprite_color": "#4CAF50",  # Green
            "error_color": "#F44336",   # Red
            "warning_color": "#FF9800", # Orange
            "always_on_top": True,
            "enable_sounds": False,
            "popup_duration": 5000,  # milliseconds
            "max_log_entries": 1000
        }
        
        self.settings = self.load_config()
    
    def load_config(self):
        """Load configuration from file or create default."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                # Merge with defaults to handle new settings
                merged_config = self.default_config.copy()
                merged_config.update(config)
                return merged_config
            else:
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.default_config.copy()
    
    def save_config(self, config=None):
        """Save configuration to file."""
        try:
            self.config_dir.mkdir(exist_ok=True)
            config_to_save = config or self.settings
            with open(self.config_file, 'w') as f:
                json.dump(config_to_save, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key, default=None):
        """Get configuration value."""
        return self.settings.get(key, default)
    
    def set(self, key, value):
        """Set configuration value and save."""
        self.settings[key] = value
        self.save_config()

# Global config instance
config = Config()
