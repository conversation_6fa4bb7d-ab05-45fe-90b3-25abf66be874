"""
Error suggestions and explanations for common Python errors.
"""

class ErrorSuggestions:
    def __init__(self):
        self.suggestions = {
            "SyntaxError": {
                "general": "Check for missing colons, parentheses, or incorrect indentation.",
                "patterns": {
                    "invalid syntax": "Look for missing colons (:) after if, for, while, def, or class statements.",
                    "unexpected EOF": "You might have unclosed parentheses, brackets, or quotes.",
                    "unmatched": "Check for unmatched parentheses, brackets, or quotes.",
                    "indentation": "Make sure your indentation is consistent (use either spaces or tabs, not both)."
                }
            },
            "NameError": {
                "general": "A variable or function name is not defined.",
                "patterns": {
                    "not defined": "Make sure you've defined the variable before using it, or check for typos.",
                    "global name": "The variable might be out of scope or misspelled."
                }
            },
            "TypeError": {
                "general": "You're using an operation on incompatible data types.",
                "patterns": {
                    "unsupported operand": "Check if you're trying to add/subtract incompatible types (like string + number).",
                    "not callable": "You might be trying to call something that isn't a function.",
                    "missing required": "A function is missing required arguments.",
                    "takes": "You're passing the wrong number of arguments to a function."
                }
            },
            "IndexError": {
                "general": "You're trying to access an index that doesn't exist.",
                "patterns": {
                    "list index out of range": "Check if your list is empty or if the index is too large.",
                    "string index out of range": "The string might be shorter than expected."
                }
            },
            "KeyError": {
                "general": "You're trying to access a dictionary key that doesn't exist.",
                "patterns": {
                    "": "Use .get() method or check if the key exists with 'in' operator."
                }
            },
            "ValueError": {
                "general": "A function received an argument of correct type but inappropriate value.",
                "patterns": {
                    "invalid literal": "Check if you're trying to convert a string to number incorrectly.",
                    "not enough values": "You might be unpacking fewer values than expected."
                }
            },
            "AttributeError": {
                "general": "An object doesn't have the attribute or method you're trying to use.",
                "patterns": {
                    "has no attribute": "Check the spelling of the attribute/method or verify the object type.",
                    "NoneType": "The variable is None - check if a function returned None unexpectedly."
                }
            },
            "IndentationError": {
                "general": "Your code indentation is incorrect.",
                "patterns": {
                    "expected an indented block": "Add proper indentation after colons (:).",
                    "unindent does not match": "Make sure your indentation levels are consistent."
                }
            },
            "ImportError": {
                "general": "Python can't import a module or package.",
                "patterns": {
                    "No module named": "Install the module with pip or check the module name spelling.",
                    "cannot import name": "The function/class might not exist in that module."
                }
            },
            "FileNotFoundError": {
                "general": "The file you're trying to access doesn't exist.",
                "patterns": {
                    "No such file": "Check the file path and make sure the file exists."
                }
            }
        }
    
    def get_suggestion(self, error_type, error_message=""):
        """Get suggestion for a specific error type and message."""
        if error_type not in self.suggestions:
            return "Check the Python documentation for this error type."
        
        error_info = self.suggestions[error_type]
        
        # Look for specific patterns in the error message
        error_message_lower = error_message.lower()
        for pattern, suggestion in error_info["patterns"].items():
            if pattern and pattern.lower() in error_message_lower:
                return suggestion
        
        # Return general suggestion if no specific pattern matches
        return error_info["general"]
    
    def get_beginner_explanation(self, error_type):
        """Get beginner-friendly explanation of error types."""
        explanations = {
            "SyntaxError": "Python can't understand your code structure.",
            "NameError": "You're using a name that Python doesn't recognize.",
            "TypeError": "You're mixing incompatible data types.",
            "IndexError": "You're trying to access a position that doesn't exist.",
            "KeyError": "You're looking for a dictionary key that doesn't exist.",
            "ValueError": "The value you provided is not what the function expected.",
            "AttributeError": "The object doesn't have the property or method you're trying to use.",
            "IndentationError": "Your code indentation (spacing) is wrong.",
            "ImportError": "Python can't find or load the module you're trying to import.",
            "FileNotFoundError": "The file you're looking for doesn't exist."
        }
        
        return explanations.get(error_type, "An error occurred in your Python code.")

# Global suggestions instance
error_suggestions = ErrorSuggestions()
