#!/usr/bin/env python3
"""
BoltBug Launcher Script
Simple launcher that handles dependencies and error checking.
"""
import sys
import subprocess
import importlib.util

def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = [
        ('PIL', 'pillow'),
        ('watchdog', 'watchdog'),
        ('psutil', 'psutil'),
        ('pyautogui', 'pyautogui'),
        ('pynput', 'pynput')
    ]
    
    missing_packages = []
    
    for import_name, package_name in required_packages:
        if importlib.util.find_spec(import_name) is None:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        print("\n💡 Or install all dependencies with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 6):
        print("❌ BoltBug requires Python 3.6 or higher")
        print(f"   Current version: {sys.version}")
        return False
    return True

def main():
    """Main launcher function."""
    print("🐛 BoltBug Launcher")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    print("✅ All dependencies satisfied")
    print("🚀 Starting BoltBug...")
    print("\n💡 Tips:")
    print("   - Right-click BoltBug for menu options")
    print("   - Drag BoltBug to move it around")
    print("   - Use test_errors.py to test error detection")
    print("   - Press Ctrl+C in terminal to exit")
    print("\n" + "=" * 30)
    
    try:
        # Import and run BoltBug
        from boltbug import main as boltbug_main
        boltbug_main()
    except KeyboardInterrupt:
        print("\n👋 BoltBug stopped by user")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all BoltBug files are in the same directory")
    except Exception as e:
        print(f"❌ Error starting BoltBug: {e}")
        print("Check the error log for more details")

if __name__ == "__main__":
    main()
