"""
Logging system for BoltBug to track errors and suggestions.
"""
import logging
import json
from datetime import datetime
from pathlib import Path
from config import config

class BoltBugLogger:
    def __init__(self):
        self.setup_logging()
        self.error_log = []
    
    def setup_logging(self):
        """Setup logging configuration."""
        # Ensure log directory exists
        config.config_dir.mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(config.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('BoltBug')
    
    def log_error_detection(self, error_info):
        """Log detected Python error with details."""
        timestamp = datetime.now().isoformat()
        
        log_entry = {
            "timestamp": timestamp,
            "error_type": error_info.get("type", "Unknown"),
            "message": error_info.get("message", ""),
            "file_path": error_info.get("file_path", ""),
            "line_number": error_info.get("line_number", 0),
            "suggestion": error_info.get("suggestion", ""),
            "code_snippet": error_info.get("code_snippet", "")
        }
        
        # Add to in-memory log
        self.error_log.append(log_entry)
        
        # Limit log size
        max_entries = config.get("max_log_entries", 1000)
        if len(self.error_log) > max_entries:
            self.error_log = self.error_log[-max_entries:]
        
        # Log to file
        self.logger.info(f"Error detected: {error_info.get('type')} in {error_info.get('file_path', 'unknown file')}")
        
        # Save detailed log
        self.save_error_log()
    
    def save_error_log(self):
        """Save error log to JSON file."""
        try:
            error_log_file = config.config_dir / "error_log.json"
            with open(error_log_file, 'w') as f:
                json.dump(self.error_log, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save error log: {e}")
    
    def get_recent_errors(self, limit=10):
        """Get recent error entries."""
        return self.error_log[-limit:] if self.error_log else []
    
    def get_error_stats(self):
        """Get statistics about detected errors."""
        if not self.error_log:
            return {}
        
        error_types = {}
        for entry in self.error_log:
            error_type = entry.get("error_type", "Unknown")
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            "total_errors": len(self.error_log),
            "error_types": error_types,
            "most_recent": self.error_log[-1]["timestamp"] if self.error_log else None
        }
    
    def log_app_event(self, event, details=""):
        """Log general application events."""
        self.logger.info(f"App event: {event} - {details}")

# Global logger instance
boltbug_logger = BoltBugLogger()
