"""
Error detection system for monitoring Python scripts and processes.
"""
import os
import re
import time
import threading
import subprocess
import psutil
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from config import config
from logger import boltbug_logger
from error_suggestions import error_suggestions

class PythonErrorHandler(FileSystemEventHandler):
    def __init__(self, error_detector):
        self.error_detector = error_detector
        self.last_modified = {}
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        if file_path.suffix in config.get("python_extensions", [".py"]):
            # Avoid duplicate processing of rapid file changes
            current_time = time.time()
            if file_path in self.last_modified:
                if current_time - self.last_modified[file_path] < 1:  # 1 second cooldown
                    return
            
            self.last_modified[file_path] = current_time
            self.error_detector.check_file_for_errors(file_path)

class ErrorDetector:
    def __init__(self):
        self.observer = Observer()
        self.monitoring = False
        self.error_callbacks = []
        self.python_processes = {}
        
    def add_error_callback(self, callback):
        """Add callback function to be called when error is detected."""
        self.error_callbacks.append(callback)
    
    def start_monitoring(self):
        """Start monitoring for Python errors."""
        if self.monitoring:
            return
        
        self.monitoring = True
        boltbug_logger.log_app_event("Error monitoring started")
        
        # Start file system monitoring
        self.start_file_monitoring()
        
        # Start process monitoring
        self.start_process_monitoring()
    
    def stop_monitoring(self):
        """Stop monitoring for errors."""
        if not self.monitoring:
            return
        
        self.monitoring = False
        self.observer.stop()
        self.observer.join()
        boltbug_logger.log_app_event("Error monitoring stopped")
    
    def start_file_monitoring(self):
        """Start monitoring Python files for changes."""
        handler = PythonErrorHandler(self)
        
        # Monitor configured directories
        for directory in config.get("monitor_directories", []):
            if os.path.exists(directory):
                self.observer.schedule(handler, directory, recursive=True)
        
        self.observer.start()
    
    def start_process_monitoring(self):
        """Start monitoring Python processes for errors."""
        def monitor_processes():
            while self.monitoring:
                try:
                    self.check_python_processes()
                    time.sleep(2)  # Check every 2 seconds
                except Exception as e:
                    boltbug_logger.logger.error(f"Process monitoring error: {e}")
        
        thread = threading.Thread(target=monitor_processes, daemon=True)
        thread.start()
    
    def check_python_processes(self):
        """Check running Python processes for errors."""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        pid = proc.info['pid']
                        cmdline = proc.info['cmdline']
                        
                        if cmdline and len(cmdline) > 1:
                            # Look for Python script in command line
                            for arg in cmdline[1:]:
                                if arg.endswith('.py') and os.path.exists(arg):
                                    self.monitor_process_output(pid, arg)
                                    break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            boltbug_logger.logger.error(f"Error checking processes: {e}")
    
    def monitor_process_output(self, pid, script_path):
        """Monitor a specific Python process for errors."""
        # This is a simplified approach - in practice, you might want to
        # capture stderr output or use more sophisticated monitoring
        pass
    
    def check_file_for_errors(self, file_path):
        """Check a Python file for syntax errors."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to compile the file to check for syntax errors
            try:
                compile(content, str(file_path), 'exec')
            except SyntaxError as e:
                error_info = {
                    "type": "SyntaxError",
                    "message": str(e.msg),
                    "file_path": str(file_path),
                    "line_number": e.lineno or 0,
                    "code_snippet": self.get_code_snippet(content, e.lineno or 1),
                    "suggestion": error_suggestions.get_suggestion("SyntaxError", str(e.msg))
                }
                self.notify_error(error_info)
                
        except Exception as e:
            boltbug_logger.logger.error(f"Error checking file {file_path}: {e}")
    
    def get_code_snippet(self, content, line_number, context=2):
        """Get code snippet around the error line."""
        lines = content.split('\n')
        start = max(0, line_number - context - 1)
        end = min(len(lines), line_number + context)
        
        snippet_lines = []
        for i in range(start, end):
            marker = ">>> " if i == line_number - 1 else "    "
            snippet_lines.append(f"{marker}{i+1}: {lines[i]}")
        
        return '\n'.join(snippet_lines)
    
    def parse_traceback(self, traceback_text):
        """Parse Python traceback to extract error information."""
        lines = traceback_text.strip().split('\n')
        
        # Find the last line with the error
        error_line = lines[-1] if lines else ""
        
        # Extract error type and message
        error_match = re.match(r'(\w+Error): (.+)', error_line)
        if not error_match:
            return None
        
        error_type = error_match.group(1)
        error_message = error_match.group(2)
        
        # Find file and line number
        file_path = ""
        line_number = 0
        
        for line in reversed(lines[:-1]):
            file_match = re.search(r'File "([^"]+)", line (\d+)', line)
            if file_match:
                file_path = file_match.group(1)
                line_number = int(file_match.group(2))
                break
        
        return {
            "type": error_type,
            "message": error_message,
            "file_path": file_path,
            "line_number": line_number,
            "traceback": traceback_text,
            "suggestion": error_suggestions.get_suggestion(error_type, error_message)
        }
    
    def notify_error(self, error_info):
        """Notify all callbacks about detected error."""
        boltbug_logger.log_error_detection(error_info)
        
        for callback in self.error_callbacks:
            try:
                callback(error_info)
            except Exception as e:
                boltbug_logger.logger.error(f"Error in callback: {e}")

# Global error detector instance
error_detector = ErrorDetector()
