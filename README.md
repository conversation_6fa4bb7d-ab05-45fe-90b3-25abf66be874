# BoltBug 🐛

A fun and educational desktop companion app that helps Python beginners learn from their coding errors!

## What is BoltBug?

BoltBug is a small, animated sprite that lives on your desktop and helps you understand Python errors. When you make a mistake in your Python code, BoltBug:

- 🎯 Detects the error automatically
- 🏃 Moves to the error location on your screen
- 🎨 Changes color and shakes to get your attention
- 💡 Shows helpful explanations and suggestions
- 📝 Logs all errors for later review

## Features

### 🎮 Interactive Sprite
- Cute animated bug that moves randomly around your desktop
- Smooth animations and visual effects
- Always stays on top but doesn't interfere with your work
- Draggable - move BoltBug wherever you want

### 🔍 Smart Error Detection
- Monitors Python files in real-time
- Detects syntax errors as you type
- Tracks running Python processes
- Supports multiple file monitoring

### 📚 Beginner-Friendly Help
- Simple explanations for common Python errors
- Specific suggestions based on error type
- Code context showing where the error occurred
- Educational approach perfect for learning

### 📊 Error Logging
- Keeps track of all detected errors
- Shows error statistics and patterns
- Helps you learn from past mistakes
- Exportable error history

## Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd boltbug
   ```

2. **Install required dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run BoltBug**
   ```bash
   python boltbug.py
   ```

## Dependencies

- `tkinter` - GUI framework (usually included with Python)
- `pillow` - Image processing for sprite graphics
- `watchdog` - File system monitoring
- `psutil` - Process monitoring
- `pyautogui` - Screen interaction
- `pynput` - Input monitoring

## How to Use

1. **Start BoltBug** by running `python boltbug.py`
2. **Write Python code** in your favorite editor
3. **Make some errors** (or use the test script)
4. **Watch BoltBug react** to your errors with helpful suggestions!

### Testing BoltBug

Use the included test script to see BoltBug in action:

```bash
python test_errors.py
```

Uncomment different error examples in the test script to trigger various error types.

## Error Types Supported

BoltBug can help with these common Python errors:

- **SyntaxError** - Code structure problems
- **NameError** - Undefined variables
- **TypeError** - Type mismatches
- **IndexError** - List/string index problems
- **KeyError** - Dictionary key issues
- **ValueError** - Invalid values
- **AttributeError** - Missing methods/properties
- **IndentationError** - Spacing problems
- **ImportError** - Module import issues
- **FileNotFoundError** - Missing files

## Configuration

BoltBug creates a configuration file at `~/.boltbug/config.json` where you can customize:

- Sprite appearance and behavior
- Error detection settings
- Popup duration and style
- Monitoring directories
- Ignored error types

## Right-Click Menu

Right-click on BoltBug to access:
- **Error Log** - View recent errors and suggestions
- **Settings** - Customize BoltBug behavior
- **Exit** - Close the application

## Educational Benefits

BoltBug is designed to help Python beginners by:

1. **Immediate Feedback** - Errors are caught and explained right away
2. **Visual Learning** - Animated reactions make errors memorable
3. **Gentle Guidance** - Friendly suggestions instead of intimidating error messages
4. **Pattern Recognition** - Error logging helps identify common mistakes
5. **Confidence Building** - Makes debugging less scary and more fun

## Troubleshooting

### BoltBug doesn't detect errors
- Make sure you're editing Python files (.py extension)
- Check that the files are in monitored directories
- Verify that error detection is enabled

### Sprite doesn't appear
- Check if the window is behind other applications
- Try moving your mouse around the screen
- Restart BoltBug if needed

### Performance issues
- Reduce monitoring directories in config
- Increase error detection interval
- Close other resource-intensive applications

## Contributing

BoltBug is designed to be educational and fun! Contributions are welcome:

- Add new error types and suggestions
- Improve sprite animations
- Enhance error detection accuracy
- Add new educational features

## License

This project is open source and available under the MIT License.

## Credits

Created with ❤️ for Python beginners everywhere!

---

**Happy debugging with BoltBug! 🐛✨**
