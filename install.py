#!/usr/bin/env python3
"""
BoltBug Installation Script
Automatically installs dependencies and sets up BoltBug.
"""
import sys
import subprocess
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 6):
        print("❌ BoltBug requires Python 3.6 or higher")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python version {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True

def install_dependencies():
    """Install required dependencies."""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    # Try to install dependencies
    commands = [
        ("pip install -r requirements.txt", "Installing dependencies"),
        ("pip install --upgrade pip", "Upgrading pip")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            # Try with pip3 if pip fails
            if "pip " in command:
                alt_command = command.replace("pip ", "pip3 ")
                print(f"🔄 Trying alternative: {alt_command}")
                if not run_command(alt_command, description):
                    return False
            else:
                return False
    
    return True

def create_desktop_shortcut():
    """Create a desktop shortcut (Windows only for now)."""
    if sys.platform == "win32":
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "BoltBug.lnk")
            target = os.path.join(os.getcwd(), "run_boltbug.py")
            wDir = os.getcwd()
            icon = target
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = icon
            shortcut.save()
            
            print("✅ Desktop shortcut created")
            return True
        except ImportError:
            print("⚠️  Could not create desktop shortcut (missing winshell)")
            return False
        except Exception as e:
            print(f"⚠️  Could not create desktop shortcut: {e}")
            return False
    else:
        print("ℹ️  Desktop shortcut creation not supported on this platform")
        return True

def test_installation():
    """Test if BoltBug can be imported and run."""
    print("🧪 Testing installation...")
    try:
        # Test imports
        import config
        import logger
        import error_detector
        import error_display
        import sprite_manager
        print("✅ All modules imported successfully")
        
        # Test basic functionality
        from config import config as app_config
        print(f"✅ Configuration loaded (sprite size: {app_config.get('sprite_size')})")
        
        return True
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False

def main():
    """Main installation function."""
    print("🐛 BoltBug Installation")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Installation failed!")
        print("Try installing dependencies manually:")
        print("   pip install pillow watchdog psutil pyautogui pynput")
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("\n❌ Installation test failed!")
        sys.exit(1)
    
    # Create desktop shortcut (optional)
    create_desktop_shortcut()
    
    print("\n" + "=" * 40)
    print("🎉 BoltBug installation completed successfully!")
    print("\n🚀 To start BoltBug:")
    print("   python run_boltbug.py")
    print("\n💡 Or use the desktop shortcut if created")
    print("\n📚 Read README.md for usage instructions")
    print("🧪 Use test_errors.py to test error detection")

if __name__ == "__main__":
    main()
