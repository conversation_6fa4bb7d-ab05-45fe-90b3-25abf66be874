"""
Error display system for showing error popups and overlays.
"""
import tkinter as tk
from tkinter import ttk
import threading
import time
from config import config
from error_suggestions import error_suggestions

class ErrorPopup:
    def __init__(self, parent=None):
        self.parent = parent
        self.popup_window = None
        self.auto_close_timer = None
    
    def show_error(self, error_info):
        """Display error popup with information and suggestions."""
        if self.popup_window:
            self.close_popup()
        
        self.create_popup_window(error_info)
        self.schedule_auto_close()
    
    def create_popup_window(self, error_info):
        """Create the popup window with error details."""
        self.popup_window = tk.Toplevel()
        self.popup_window.title("BoltBug - Python Error Detected!")
        self.popup_window.geometry("500x400")
        self.popup_window.configure(bg='#f0f0f0')
        
        # Make window stay on top
        self.popup_window.attributes('-topmost', True)
        
        # Center the window
        self.center_window()
        
        # Create main frame
        main_frame = ttk.Frame(self.popup_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Error type header
        error_type = error_info.get("type", "Unknown Error")
        header_label = ttk.Label(
            main_frame, 
            text=f"🐛 {error_type} Detected!",
            font=("Arial", 16, "bold"),
            foreground="#d32f2f"
        )
        header_label.pack(pady=(0, 10))
        
        # Beginner explanation
        explanation = error_suggestions.get_beginner_explanation(error_type)
        explanation_label = ttk.Label(
            main_frame,
            text=explanation,
            font=("Arial", 11),
            foreground="#666666",
            wraplength=450
        )
        explanation_label.pack(pady=(0, 15))
        
        # Error details frame
        details_frame = ttk.LabelFrame(main_frame, text="Error Details", padding="10")
        details_frame.pack(fill=tk.X, pady=(0, 15))
        
        # File and line info
        if error_info.get("file_path"):
            file_label = ttk.Label(
                details_frame,
                text=f"File: {error_info['file_path']}",
                font=("Consolas", 9)
            )
            file_label.pack(anchor=tk.W)
        
        if error_info.get("line_number"):
            line_label = ttk.Label(
                details_frame,
                text=f"Line: {error_info['line_number']}",
                font=("Consolas", 9)
            )
            line_label.pack(anchor=tk.W)
        
        # Error message
        if error_info.get("message"):
            message_label = ttk.Label(
                details_frame,
                text=f"Message: {error_info['message']}",
                font=("Consolas", 9),
                wraplength=450
            )
            message_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Suggestion frame
        suggestion_frame = ttk.LabelFrame(main_frame, text="💡 Suggestion", padding="10")
        suggestion_frame.pack(fill=tk.X, pady=(0, 15))
        
        suggestion_text = error_info.get("suggestion", "Check the Python documentation for this error.")
        suggestion_label = ttk.Label(
            suggestion_frame,
            text=suggestion_text,
            font=("Arial", 10),
            wraplength=450,
            foreground="#2e7d32"
        )
        suggestion_label.pack(anchor=tk.W)
        
        # Code snippet if available
        if error_info.get("code_snippet"):
            code_frame = ttk.LabelFrame(main_frame, text="Code Context", padding="10")
            code_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
            
            code_text = tk.Text(
                code_frame,
                height=6,
                font=("Consolas", 9),
                bg="#f8f8f8",
                fg="#333333",
                wrap=tk.NONE
            )
            code_text.pack(fill=tk.BOTH, expand=True)
            code_text.insert(tk.END, error_info["code_snippet"])
            code_text.config(state=tk.DISABLED)
            
            # Add scrollbar
            scrollbar = ttk.Scrollbar(code_frame, orient=tk.VERTICAL, command=code_text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            code_text.config(yscrollcommand=scrollbar.set)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        # Close button
        close_button = ttk.Button(
            buttons_frame,
            text="Got it!",
            command=self.close_popup
        )
        close_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Don't show again button
        ignore_button = ttk.Button(
            buttons_frame,
            text="Ignore this error type",
            command=lambda: self.ignore_error_type(error_type)
        )
        ignore_button.pack(side=tk.RIGHT)
        
        # Bind escape key to close
        self.popup_window.bind('<Escape>', lambda e: self.close_popup())
        
        # Focus the window
        self.popup_window.focus_force()
    
    def center_window(self):
        """Center the popup window on screen."""
        self.popup_window.update_idletasks()
        width = self.popup_window.winfo_width()
        height = self.popup_window.winfo_height()
        x = (self.popup_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.popup_window.winfo_screenheight() // 2) - (height // 2)
        self.popup_window.geometry(f"{width}x{height}+{x}+{y}")
    
    def schedule_auto_close(self):
        """Schedule automatic closing of popup."""
        duration = config.get("popup_duration", 5000)  # milliseconds
        
        def auto_close():
            time.sleep(duration / 1000)
            if self.popup_window:
                try:
                    self.popup_window.after(0, self.close_popup)
                except:
                    pass
        
        self.auto_close_timer = threading.Thread(target=auto_close, daemon=True)
        self.auto_close_timer.start()
    
    def close_popup(self):
        """Close the popup window."""
        if self.popup_window:
            self.popup_window.destroy()
            self.popup_window = None
    
    def ignore_error_type(self, error_type):
        """Add error type to ignore list."""
        ignored_errors = config.get("ignored_error_types", [])
        if error_type not in ignored_errors:
            ignored_errors.append(error_type)
            config.set("ignored_error_types", ignored_errors)
        self.close_popup()

class ErrorOverlay:
    def __init__(self, parent):
        self.parent = parent
        self.overlay = None
    
    def show_overlay(self, message, duration=2000):
        """Show a brief overlay message."""
        if self.overlay:
            self.overlay.destroy()
        
        self.overlay = tk.Toplevel(self.parent)
        self.overlay.overrideredirect(True)
        self.overlay.attributes('-topmost', True)
        self.overlay.configure(bg='#ff5722')
        
        label = tk.Label(
            self.overlay,
            text=message,
            bg='#ff5722',
            fg='white',
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=10
        )
        label.pack()
        
        # Position near the sprite
        if hasattr(self.parent, 'x') and hasattr(self.parent, 'y'):
            x = self.parent.x + 70
            y = self.parent.y - 30
        else:
            x = 100
            y = 100
        
        self.overlay.geometry(f"+{x}+{y}")
        
        # Auto-close after duration
        self.overlay.after(duration, self.close_overlay)
    
    def close_overlay(self):
        """Close the overlay."""
        if self.overlay:
            self.overlay.destroy()
            self.overlay = None
