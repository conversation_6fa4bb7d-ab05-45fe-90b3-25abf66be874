"""
Sprite animation and movement system for BoltBug.
"""
import tkinter as tk
import math
import random
import time
from PIL import Image, ImageTk, ImageDraw
from config import config

class BoltBugSprite:
    def __init__(self, parent):
        self.parent = parent
        self.size = config.get("sprite_size", 64)
        self.x = random.randint(100, 800)
        self.y = random.randint(100, 600)
        self.target_x = self.x
        self.target_y = self.y
        
        # Movement properties
        self.speed = config.get("movement_speed", 2)
        self.is_moving_to_error = False
        self.movement_mode = "random"  # "random", "targeted", "idle"
        
        # Animation properties
        self.current_color = config.get("sprite_color", "#4CAF50")
        self.original_color = self.current_color
        self.is_shaking = False
        self.shake_intensity = 0
        self.shake_offset_x = 0
        self.shake_offset_y = 0
        
        # Create sprite image
        self.create_sprite_image()
        
        # Animation state
        self.animation_frame = 0
        self.last_blink = time.time()
        self.blink_interval = random.uniform(3, 8)  # Random blink interval
    
    def create_sprite_image(self):
        """Create the BoltBug sprite image."""
        # Create a simple bug-like sprite
        img = Image.new('RGBA', (self.size, self.size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Convert hex color to RGB
        color = self.hex_to_rgb(self.current_color)
        
        # Body (oval)
        body_margin = 8
        draw.ellipse([
            body_margin, body_margin + 10,
            self.size - body_margin, self.size - body_margin
        ], fill=color, outline=(0, 0, 0, 255), width=2)
        
        # Head (smaller circle)
        head_size = 20
        head_x = (self.size - head_size) // 2
        head_y = 5
        draw.ellipse([
            head_x, head_y,
            head_x + head_size, head_y + head_size
        ], fill=color, outline=(0, 0, 0, 255), width=2)
        
        # Eyes
        eye_size = 4
        left_eye_x = head_x + 4
        right_eye_x = head_x + head_size - 8
        eye_y = head_y + 6
        
        draw.ellipse([
            left_eye_x, eye_y,
            left_eye_x + eye_size, eye_y + eye_size
        ], fill=(0, 0, 0, 255))
        
        draw.ellipse([
            right_eye_x, eye_y,
            right_eye_x + eye_size, eye_y + eye_size
        ], fill=(0, 0, 0, 255))
        
        # Antennae
        antenna_start_x1 = head_x + 6
        antenna_start_x2 = head_x + head_size - 6
        antenna_start_y = head_y + 2
        antenna_end_y = head_y - 8
        
        draw.line([
            antenna_start_x1, antenna_start_y,
            antenna_start_x1 - 5, antenna_end_y
        ], fill=(0, 0, 0, 255), width=2)
        
        draw.line([
            antenna_start_x2, antenna_start_y,
            antenna_start_x2 + 5, antenna_end_y
        ], fill=(0, 0, 0, 255), width=2)
        
        # Antenna tips
        draw.ellipse([
            antenna_start_x1 - 7, antenna_end_y - 2,
            antenna_start_x1 - 3, antenna_end_y + 2
        ], fill=(255, 255, 0, 255))
        
        draw.ellipse([
            antenna_start_x2 + 3, antenna_end_y - 2,
            antenna_start_x2 + 7, antenna_end_y + 2
        ], fill=(255, 255, 0, 255))
        
        # Legs (simple lines)
        leg_positions = [
            (body_margin + 5, body_margin + 20),
            (body_margin + 5, body_margin + 30),
            (self.size - body_margin - 5, body_margin + 20),
            (self.size - body_margin - 5, body_margin + 30)
        ]
        
        for i, (leg_x, leg_y) in enumerate(leg_positions):
            if i < 2:  # Left legs
                draw.line([leg_x, leg_y, leg_x - 10, leg_y + 8], fill=(0, 0, 0, 255), width=2)
            else:  # Right legs
                draw.line([leg_x, leg_y, leg_x + 10, leg_y + 8], fill=(0, 0, 0, 255), width=2)
        
        self.image = ImageTk.PhotoImage(img)
    
    def hex_to_rgb(self, hex_color):
        """Convert hex color to RGB tuple."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def update_position(self):
        """Update sprite position based on movement mode."""
        if self.movement_mode == "random":
            self.random_movement()
        elif self.movement_mode == "targeted":
            self.targeted_movement()
        
        # Apply shake effect if active
        if self.is_shaking:
            self.apply_shake_effect()
        
        # Update animation frame
        self.animation_frame += 1
        
        # Handle blinking
        current_time = time.time()
        if current_time - self.last_blink > self.blink_interval:
            self.blink()
            self.last_blink = current_time
            self.blink_interval = random.uniform(3, 8)
    
    def random_movement(self):
        """Implement random movement pattern."""
        # Occasionally pick a new random target
        if random.random() < 0.02:  # 2% chance each frame
            screen_width = self.parent.winfo_screenwidth()
            screen_height = self.parent.winfo_screenheight()
            
            self.target_x = random.randint(50, screen_width - 100)
            self.target_y = random.randint(50, screen_height - 100)
        
        # Move towards target
        self.move_towards_target()
    
    def targeted_movement(self):
        """Move directly towards the target (error location)."""
        self.move_towards_target()
        
        # Check if we've reached the target
        distance = math.sqrt((self.x - self.target_x)**2 + (self.y - self.target_y)**2)
        if distance < 10:
            self.movement_mode = "idle"
    
    def move_towards_target(self):
        """Move sprite towards the current target."""
        dx = self.target_x - self.x
        dy = self.target_y - self.y
        distance = math.sqrt(dx**2 + dy**2)
        
        if distance > self.speed:
            # Normalize and apply speed
            self.x += (dx / distance) * self.speed
            self.y += (dy / distance) * self.speed
        else:
            # Close enough, snap to target
            self.x = self.target_x
            self.y = self.target_y
    
    def move_to_error_location(self, error_info):
        """Move sprite to error location."""
        # For now, move to a position near the center of the screen
        # In a more advanced implementation, you could try to detect
        # the actual window/editor position
        screen_width = self.parent.winfo_screenwidth()
        screen_height = self.parent.winfo_screenheight()
        
        self.target_x = screen_width // 2
        self.target_y = screen_height // 2
        self.movement_mode = "targeted"
        self.is_moving_to_error = True
        
        # Change color to indicate error state
        self.change_color(config.get("error_color", "#F44336"))
        
        # Start shaking
        self.start_shaking(intensity=5)
    
    def return_to_normal(self):
        """Return sprite to normal state after error handling."""
        self.movement_mode = "random"
        self.is_moving_to_error = False
        self.change_color(self.original_color)
        self.stop_shaking()
    
    def change_color(self, new_color):
        """Change sprite color."""
        if self.current_color != new_color:
            self.current_color = new_color
            self.create_sprite_image()
    
    def start_shaking(self, intensity=3):
        """Start shaking animation."""
        self.is_shaking = True
        self.shake_intensity = intensity
    
    def stop_shaking(self):
        """Stop shaking animation."""
        self.is_shaking = False
        self.shake_intensity = 0
        self.shake_offset_x = 0
        self.shake_offset_y = 0
    
    def apply_shake_effect(self):
        """Apply shake effect to sprite position."""
        if self.shake_intensity > 0:
            self.shake_offset_x = random.randint(-self.shake_intensity, self.shake_intensity)
            self.shake_offset_y = random.randint(-self.shake_intensity, self.shake_intensity)
    
    def blink(self):
        """Simple blink animation by briefly changing eye color."""
        # This is a placeholder - in a more advanced version,
        # you could create separate images for blinking
        pass
    
    def get_display_position(self):
        """Get the current display position including shake offset."""
        return (
            int(self.x + self.shake_offset_x),
            int(self.y + self.shake_offset_y)
        )
