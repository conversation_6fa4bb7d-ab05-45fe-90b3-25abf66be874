"""
Test script to generate various Python errors for testing BoltBug.
Run this script to see BoltBug in action!
"""

def test_syntax_error():
    """Test syntax errors."""
    print("Testing syntax errors...")
    # Uncomment the lines below to test different syntax errors:
    
    # Missing colon
    # if True
    #     print("Missing colon")
    
    # Unmatched parentheses
    # print("Hello world"
    
    # Invalid indentation
    # if True:
    # print("Bad indentation")

def test_name_error():
    """Test name errors."""
    print("Testing name errors...")
    # Uncomment to test:
    # print(undefined_variable)

def test_type_error():
    """Test type errors."""
    print("Testing type errors...")
    # Uncomment to test:
    # result = "hello" + 5

def test_index_error():
    """Test index errors."""
    print("Testing index errors...")
    # Uncomment to test:
    # my_list = [1, 2, 3]
    # print(my_list[10])

def test_key_error():
    """Test key errors."""
    print("Testing key errors...")
    # Uncomment to test:
    # my_dict = {"a": 1, "b": 2}
    # print(my_dict["c"])

def test_value_error():
    """Test value errors."""
    print("Testing value errors...")
    # Uncomment to test:
    # int("not_a_number")

def test_attribute_error():
    """Test attribute errors."""
    print("Testing attribute errors...")
    # Uncomment to test:
    # my_string = "hello"
    # my_string.nonexistent_method()

if __name__ == "__main__":
    print("BoltBug Error Test Script")
    print("Uncomment lines in the functions to test different error types.")
    print("Make sure BoltBug is running to see it detect the errors!")
    
    # Run tests
    test_syntax_error()
    test_name_error()
    test_type_error()
    test_index_error()
    test_key_error()
    test_value_error()
    test_attribute_error()
    
    print("Test completed!")
