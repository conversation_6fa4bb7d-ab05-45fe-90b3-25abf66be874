"""
BoltBug - Desktop companion app for Python error detection and assistance.
"""
import tkinter as tk
from tkinter import messagebox
import threading
import time
import sys
from config import config
from logger import boltbug_logger
from error_detector import error_detector
from error_display import ErrorPopup, ErrorOverlay
from sprite_manager import BoltBugSprite

class BoltBugApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        
        # Initialize components
        self.sprite = BoltBugSprite(self.root)
        self.error_popup = ErrorPopup(self.root)
        self.error_overlay = ErrorOverlay(self.root)
        
        # State management
        self.running = True
        self.last_error_time = 0
        
        # Setup error detection
        error_detector.add_error_callback(self.on_error_detected)
        
        # Create sprite label
        self.sprite_label = tk.Label(
            self.root,
            image=self.sprite.image,
            bg='black'
        )
        self.sprite_label.pack()
        
        # Start animation loop
        self.animate()
        
        # Setup window close handler
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Start error monitoring
        self.start_error_monitoring()
        
        boltbug_logger.log_app_event("BoltBug started")
    
    def setup_window(self):
        """Setup the main application window."""
        self.root.title("BoltBug")
        self.root.geometry(f"{config.get('sprite_size', 64)}x{config.get('sprite_size', 64)}")
        
        # Make window transparent and always on top
        self.root.attributes('-topmost', config.get('always_on_top', True))
        self.root.overrideredirect(True)  # Remove window decorations
        self.root.configure(bg='black')
        
        # Set transparency key
        self.root.wm_attributes('-transparentcolor', 'black')
        
        # Make window draggable
        self.make_draggable()
    
    def make_draggable(self):
        """Make the window draggable."""
        def start_drag(event):
            self.root.x = event.x
            self.root.y = event.y
        
        def drag_window(event):
            x = self.root.winfo_pointerx() - self.root.x
            y = self.root.winfo_pointery() - self.root.y
            self.root.geometry(f"+{x}+{y}")
            
            # Update sprite position to match window
            self.sprite.x = x
            self.sprite.y = y
        
        self.root.bind('<Button-1>', start_drag)
        self.root.bind('<B1-Motion>', drag_window)
    
    def start_error_monitoring(self):
        """Start error monitoring in a separate thread."""
        def monitor():
            try:
                error_detector.start_monitoring()
            except Exception as e:
                boltbug_logger.logger.error(f"Error monitoring failed: {e}")
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def animate(self):
        """Main animation loop."""
        if not self.running:
            return
        
        try:
            # Update sprite position and animation
            self.sprite.update_position()
            
            # Get display position
            display_x, display_y = self.sprite.get_display_position()
            
            # Update window position
            self.root.geometry(f"+{display_x}+{display_y}")
            
            # Update sprite image if color changed
            if hasattr(self.sprite, 'image'):
                self.sprite_label.configure(image=self.sprite.image)
            
        except Exception as e:
            boltbug_logger.logger.error(f"Animation error: {e}")
        
        # Schedule next animation frame
        interval = config.get("random_movement_interval", 100)
        self.root.after(interval, self.animate)
    
    def on_error_detected(self, error_info):
        """Handle detected Python errors."""
        try:
            current_time = time.time()
            
            # Avoid spam - only process errors if enough time has passed
            if current_time - self.last_error_time < 2:  # 2 second cooldown
                return
            
            self.last_error_time = current_time
            
            # Check if this error type is ignored
            ignored_errors = config.get("ignored_error_types", [])
            if error_info.get("type") in ignored_errors:
                return
            
            boltbug_logger.log_app_event("Error detected", f"Type: {error_info.get('type')}")
            
            # Move sprite to error location
            self.sprite.move_to_error_location(error_info)
            
            # Show error overlay briefly
            error_type = error_info.get("type", "Error")
            self.error_overlay.show_overlay(f"🐛 {error_type} detected!", 2000)
            
            # Show detailed error popup after a short delay
            self.root.after(1500, lambda: self.show_error_popup(error_info))
            
            # Return to normal after popup duration
            popup_duration = config.get("popup_duration", 5000)
            self.root.after(popup_duration + 2000, self.return_to_normal)
            
        except Exception as e:
            boltbug_logger.logger.error(f"Error handling error: {e}")
    
    def show_error_popup(self, error_info):
        """Show the detailed error popup."""
        try:
            self.error_popup.show_error(error_info)
        except Exception as e:
            boltbug_logger.logger.error(f"Error showing popup: {e}")
    
    def return_to_normal(self):
        """Return BoltBug to normal state."""
        try:
            self.sprite.return_to_normal()
        except Exception as e:
            boltbug_logger.logger.error(f"Error returning to normal: {e}")
    
    def create_context_menu(self):
        """Create right-click context menu."""
        menu = tk.Menu(self.root, tearoff=0)
        menu.add_command(label="Show Error Log", command=self.show_error_log)
        menu.add_command(label="Settings", command=self.show_settings)
        menu.add_separator()
        menu.add_command(label="Exit", command=self.on_closing)
        
        def show_menu(event):
            try:
                menu.tk_popup(event.x_root, event.y_root)
            finally:
                menu.grab_release()
        
        self.root.bind('<Button-3>', show_menu)  # Right click
    
    def show_error_log(self):
        """Show error log window."""
        log_window = tk.Toplevel(self.root)
        log_window.title("BoltBug Error Log")
        log_window.geometry("600x400")
        
        # Create text widget with scrollbar
        frame = tk.Frame(log_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget = tk.Text(frame, wrap=tk.WORD)
        scrollbar = tk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        # Get recent errors
        recent_errors = boltbug_logger.get_recent_errors(20)
        
        if recent_errors:
            for error in recent_errors:
                text_widget.insert(tk.END, f"[{error['timestamp']}] {error['error_type']}\n")
                text_widget.insert(tk.END, f"File: {error['file_path']}\n")
                text_widget.insert(tk.END, f"Line: {error['line_number']}\n")
                text_widget.insert(tk.END, f"Message: {error['message']}\n")
                text_widget.insert(tk.END, f"Suggestion: {error['suggestion']}\n")
                text_widget.insert(tk.END, "-" * 50 + "\n\n")
        else:
            text_widget.insert(tk.END, "No errors detected yet.")
        
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def show_settings(self):
        """Show settings window."""
        # This would open a settings dialog
        # For now, just show a simple message
        tk.messagebox.showinfo("Settings", "Settings panel coming soon!")
    
    def on_closing(self):
        """Handle application closing."""
        boltbug_logger.log_app_event("BoltBug closing")
        self.running = False
        
        try:
            error_detector.stop_monitoring()
        except:
            pass
        
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """Start the application."""
        try:
            # Add context menu
            self.create_context_menu()
            
            # Start the main loop
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
        except Exception as e:
            boltbug_logger.logger.error(f"Application error: {e}")
            self.on_closing()

def main():
    """Main entry point."""
    try:
        app = BoltBugApp()
        app.run()
    except Exception as e:
        print(f"Failed to start BoltBug: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
